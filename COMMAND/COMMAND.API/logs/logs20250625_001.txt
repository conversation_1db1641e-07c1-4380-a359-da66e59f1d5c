2025-06-25 13:09:40.983 +07:00 [WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductTag'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-25 13:09:41.022 +07:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-25 13:09:41.232 +07:00 [WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductTag'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-25 13:09:50.926 +07:00 [WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductTag'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-25 13:09:50.957 +07:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-25 13:10:51.379 +07:00 [WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductTag'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-25 13:10:51.432 +07:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-25 13:10:52.577 +07:00 [ERR] Tag was not found.
CONTRACT.CONTRACT.DOMAIN.Exceptions.TagException+TagNotFoundException: Tag was not found.
   at COMMAND.APPLICATION.UseCases.Commands.Products.CreateProductCommandHandler.Handle(CreateProductCommand request, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\COMMAND\COMMAND.APPLICATION\UseCases\Commands\Products\CreateProductCommandHandler.cs:line 35
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.TracingPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\TracingPipelineBehavior.cs:line 16
   at COMMAND.APPLICATION.Behaviors.TransactionPipelineBehavior`2.<>c__DisplayClass2_0.<<Handle>b__0>d.MoveNext() in D:\E_COMMERCE_TEDDYBEAR_SHOP\COMMAND\COMMAND.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 25
--- End of stack trace from previous location ---
   at COMMAND.APPLICATION.Behaviors.TransactionPipelineBehavior`2.<>c__DisplayClass2_0.<<Handle>b__0>d.MoveNext() in D:\E_COMMERCE_TEDDYBEAR_SHOP\COMMAND\COMMAND.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 28
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at COMMAND.APPLICATION.Behaviors.TransactionPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\COMMAND\COMMAND.APPLICATION\Behaviors\TransactionPipelineBehavior.cs:line 21
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.PerformancePipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\PerformancePipelineBehavior.cs:line 16
   at CONTRACT.CONTRACT.APPLICATION.Behaviors.ValidationPipelineBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in D:\E_COMMERCE_TEDDYBEAR_SHOP\CONTRACT\CONTRACT\CONTRACT.APPLICATION\Behaviors\ValidationPipelineBehavior.cs:line 19
   at COMMAND.PRESENTATION.Apis.ProductApi.CreateProduct(CreateProductCommand command, ISender sender) in D:\E_COMMERCE_TEDDYBEAR_SHOP\COMMAND\COMMAND.PRESENTATION\Apis\ProductApi.cs:line 24
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.ExecuteTaskResult[T](Task`1 task, HttpContext httpContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.<>c__DisplayClass102_2.<<HandleRequestBodyAndCompileRequestDelegateForJson>b__2>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at COMMAND.API.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next) in D:\E_COMMERCE_TEDDYBEAR_SHOP\COMMAND\COMMAND.API\Middlewares\ExceptionHandlingMiddleware.cs:line 12
2025-06-25 15:49:42.848 +07:00 [WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductTag'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-25 15:49:42.882 +07:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-25 15:49:43.019 +07:00 [WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductTag'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-25 15:49:51.051 +07:00 [WRN] Entity 'Product' has a global query filter defined and is the required end of a relationship with the entity 'ProductTag'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-25 15:49:51.087 +07:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
