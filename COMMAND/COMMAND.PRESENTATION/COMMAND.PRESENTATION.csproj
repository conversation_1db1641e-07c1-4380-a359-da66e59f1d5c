<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\AUTHORIZATION\AUTHORIZATION.CONTRACT\AUTHORIZATION.CONTRACT.csproj" />
        <ProjectReference Include="..\..\CONTRACT\CONTRACT\CONTRACT.csproj"/>
        <ProjectReference Include="..\COMMAND.CONTRACT\COMMAND.CONTRACT.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Carter" Version="8.2.1"/>
    </ItemGroup>

</Project>
