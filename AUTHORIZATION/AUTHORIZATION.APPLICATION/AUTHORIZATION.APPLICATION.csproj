<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\CONTRACT\CONTRACT\CONTRACT.csproj"/>
        <ProjectReference Include="..\..\QUERY\QUERY.PERSISTENCE\QUERY.PERSISTENCE.csproj"/>
        <ProjectReference Include="..\AUTHORIZATION.CONTRACT\AUTHORIZATION.CONTRACT.csproj"/>
        <ProjectReference Include="..\AUTHORIZATION.PERSISTENCE\AUTHORIZATION.PERSISTENCE.csproj"/>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="BCrypt" Version="1.0.0" />
    </ItemGroup>

</Project>
