<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <Folder Include="CONTRACT.API\"/>
        <Folder Include="CONTRACT.APPLICATION\"/>
        <Folder Include="CONTRACT.CONTRACT\Services\"/>
        <Folder Include="CONTRACT.INFRASTRUCTURE\DependencyInjection\Extensions\"/>
        <Folder Include="CONTRACT.PRESENTATION\"/>
        <Folder Include="CONTRACT.PERSISTENCE\"/>
        <Folder Include="CONTRACT.INFRASTRUCTURE\"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Carter" Version="8.2.1"/>
        <PackageReference Include="Ardalis.SmartEnum" Version="8.2.0"/>
        <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0"/>
        <PackageReference Include="CloudinaryDotNet" Version="1.27.5"/>
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="8.1.4"/>

        <PackageReference Include="FluentValidation" Version="11.11.0"/>
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0"/>
        <PackageReference Include="MailKit" Version="4.12.1"/>
        <PackageReference Include="MediatR" Version="12.5.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="8.0.17"/>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.17"/>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.17"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.17"/>
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.6"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6"/>
        <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.12.0"/>
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3"/>
        <PackageReference Include="Serilog" Version="4.3.0"/>
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0"/>
        <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.4"/>
        <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="8.1.4"/>
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.0"/>
    </ItemGroup>

</Project>
